import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { User, Mail, Calendar, Trophy, Code, Target, Clock, X } from 'lucide-react';

const Profile = () => {
  const { user } = useAuth();
  const [showSolutions, setShowSolutions] = useState(false);
  const [solutions, setSolutions] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchUserSolutions = async () => {
    if (!user?._id) return;

    setLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/solutions/user/${user._id}`, {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSolutions(data.solutions || []);
      } else {
        console.error('Failed to fetch solutions');
        setSolutions([]);
      }
    } catch (error) {
      console.error('Error fetching solutions:', error);
      setSolutions([]);
    } finally {
      setLoading(false);
    }
  };

  const handleViewSolutions = () => {
    setShowSolutions(true);
    fetchUserSolutions();
  };

  // Get first letter of username or full name for avatar
  const getAvatarLetter = () => {
    if (user?.username) {
      return user.username.charAt(0).toUpperCase();
    }
    if (user?.fullName) {
      return user.fullName.charAt(0).toUpperCase();
    }
    return 'U';
  };

  return (
    <div className="min-h-screen bg-[#1a0b2e] px-4 py-8">
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-600/20 rounded-full blur-[100px] animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-600/20 rounded-full blur-[100px] animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-600 text-transparent bg-clip-text mb-2">
            User Profile
          </h1>
          <p className="text-gray-400">Manage your account and view your progress</p>
        </div>

        {/* Profile Card */}
        <div className="bg-gray-900/60 backdrop-blur-sm rounded-2xl border border-purple-500/30 shadow-2xl overflow-hidden">
          {/* Profile Header */}
          <div className="bg-gradient-to-r from-purple-600 to-pink-600 px-8 py-12 text-center">
            <div className="w-24 h-24 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white font-bold text-3xl mx-auto mb-4 shadow-xl">
              {getAvatarLetter()}
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">
              {user?.fullName || 'User'}
            </h2>
            <p className="text-purple-100">
              @{user?.username || 'username'}
            </p>
          </div>

          {/* Profile Content */}
          <div className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Personal Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-white flex items-center gap-2">
                  <User size={20} className="text-purple-400" />
                  Personal Information
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-4 bg-purple-500/10 rounded-lg border border-purple-500/20">
                    <User size={18} className="text-purple-400" />
                    <div>
                      <div className="text-sm text-gray-400">Full Name</div>
                      <div className="text-white font-medium">{user?.fullName || 'Not provided'}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3 p-4 bg-purple-500/10 rounded-lg border border-purple-500/20">
                    <Mail size={18} className="text-purple-400" />
                    <div>
                      <div className="text-sm text-gray-400">Email</div>
                      <div className="text-white font-medium">{user?.email || 'Not provided'}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3 p-4 bg-purple-500/10 rounded-lg border border-purple-500/20">
                    <Calendar size={18} className="text-purple-400" />
                    <div>
                      <div className="text-sm text-gray-400">Member Since</div>
                      <div className="text-white font-medium">
                        {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Unknown'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Statistics */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-white flex items-center gap-2">
                  <Trophy size={20} className="text-purple-400" />
                  Statistics
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-4 bg-purple-500/10 rounded-lg border border-purple-500/20">
                    <Code size={18} className="text-purple-400" />
                    <div>
                      <div className="text-sm text-gray-400">Problems Solved</div>
                      <div className="text-white font-medium text-2xl">0</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3 p-4 bg-purple-500/10 rounded-lg border border-purple-500/20">
                    <Target size={18} className="text-purple-400" />
                    <div>
                      <div className="text-sm text-gray-400">Success Rate</div>
                      <div className="text-white font-medium text-2xl">0%</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3 p-4 bg-purple-500/10 rounded-lg border border-purple-500/20">
                    <Trophy size={18} className="text-purple-400" />
                    <div>
                      <div className="text-sm text-gray-400">Rank</div>
                      <div className="text-white font-medium text-2xl">Beginner</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="mt-8 flex flex-col sm:flex-row gap-4">
              <button className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 px-6 rounded-lg font-medium hover:from-purple-700 hover:to-pink-700 transition-all duration-200 transform hover:-translate-y-0.5 shadow-lg hover:shadow-xl">
                Edit Profile
              </button>
              <button
                onClick={handleViewSolutions}
                className="flex-1 bg-gray-700 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-600 transition-all duration-200"
              >
                View Solutions
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Solutions Modal */}
      {showSolutions && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-xl border border-purple-500/20 shadow-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-2xl font-bold text-white flex items-center gap-2">
                <Code className="text-purple-400" size={24} />
                My Solutions
              </h2>
              <button
                onClick={() => setShowSolutions(false)}
                className="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-700 rounded-lg"
              >
                <X size={20} />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[60vh]">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                  <span className="ml-3 text-gray-400">Loading solutions...</span>
                </div>
              ) : solutions.length === 0 ? (
                <div className="text-center py-12">
                  <Code className="mx-auto text-gray-500 mb-4" size={48} />
                  <p className="text-gray-400 text-lg">No solutions found</p>
                  <p className="text-gray-500 text-sm mt-2">Start solving problems to see your solutions here!</p>
                </div>
              ) : (
                <div className="grid gap-4">
                  {solutions.map((solution, index) => (
                    <div key={solution.id} className="bg-gray-800/50 rounded-lg border border-gray-700 p-4 hover:border-purple-500/30 transition-colors">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="text-white font-medium text-lg">{solution.questionTitle}</h3>
                          <div className="flex items-center gap-3 mt-1">
                            <span className={`px-2 py-1 rounded text-xs font-medium ${
                              solution.difficulty === 'Easy' ? 'bg-green-500/20 text-green-400' :
                              solution.difficulty === 'Medium' ? 'bg-yellow-500/20 text-yellow-400' :
                              'bg-red-500/20 text-red-400'
                            }`}>
                              {solution.difficulty}
                            </span>
                            <span className="bg-purple-500/20 text-purple-300 px-2 py-1 rounded text-xs font-medium">
                              {solution.language}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 text-gray-400 text-sm">
                          <Clock size={14} />
                          {new Date(solution.timestamp).toLocaleDateString()}
                        </div>
                      </div>

                      {solution.approach && (
                        <div className="mb-3">
                          <p className="text-gray-300 text-sm"><strong>Approach:</strong> {solution.approach}</p>
                        </div>
                      )}

                      <details className="group">
                        <summary className="cursor-pointer text-purple-400 hover:text-purple-300 text-sm font-medium">
                          View Code
                        </summary>
                        <div className="mt-3 bg-gray-900 rounded-lg p-4 border border-gray-600">
                          <pre className="text-gray-300 text-sm overflow-x-auto whitespace-pre-wrap">
                            <code>{solution.code}</code>
                          </pre>
                        </div>
                      </details>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Profile;
