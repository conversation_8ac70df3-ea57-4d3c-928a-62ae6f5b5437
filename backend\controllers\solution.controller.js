/**
 * Solution controller
 * Handles HTTP requests for solution-related endpoints
 */
import asyncHandler from 'express-async-handler';
import { StatusCodes } from 'http-status-codes';
import Solution from '../models/solution.model.js';
import User from '../models/user.model.js';
import Question from '../models/question.model.js';

/**
 * Save a solution submission to the database
 * @route POST /api/v1/solutions
 * @access Private (requires authentication)
 */
const saveSolution = asyncHandler(async (req, res) => {
  const {
    questionTitle,
    code,
    language,
    approach
  } = req.body;

  // Get user from request (set by auth middleware) or use default for testing
  let userId, user;

  if (req.user && req.user.id) {
    userId = req.user.id;
    user = await User.findById(userId);
  } else {
    // For testing: find any existing user
    user = await User.findOne();
    if (!user) {
      res.status(StatusCodes.NOT_FOUND);
      throw new Error('No users found in database');
    }
    userId = user._id;
  }

  if (!user) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('User not found');
  }

  // Validate required fields
  if (!questionTitle || !code || !language) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Missing required fields: questionTitle, code, language');
  }

  // Find the question by title
  const question = await Question.findOne({ title: questionTitle });
  if (!question) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Question not found');
  }

  // Create new solution
  const solution = await Solution.create({
    user: userId,
    question: question._id,
    code,
    language,
    approach: approach || ''
  });

  // Update user's sol map
  const mapKey = questionTitle;
  const existingSolutions = user.sol.get(mapKey);

  if (existingSolutions) {
    user.sol.set(mapKey, [...existingSolutions, solution._id]);
  } else {
    user.sol.set(mapKey, [solution._id]);
  }

  // Add user to question's userList if not already present
  if (!question.userList.includes(userId)) {
    question.userList.push(userId);
    await question.save();
  }

  await user.save();

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Solution saved successfully',
    solution: {
      id: solution._id,
      questionTitle: question.title,
      language: solution.language,
      createdAt: solution.createdAt
    }
  });
});

/**
 * Get all solutions for a specific question (leaderboard)
 * @route GET /api/v1/solutions/problem/:questionTitle
 * @access Public
 */
const getSolutionsByProblem = asyncHandler(async (req, res) => {
  const { questionTitle } = req.params;
  const { limit = 50, page = 1 } = req.query;

  // Find the question first
  const question = await Question.findOne({ title: questionTitle });
  if (!question) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Question not found');
  }

  // Get all solutions for this question, sorted by submission time
  const solutions = await Solution.find({
    question: question._id
  })
  .populate('user', 'fullName username')
  .populate('question', 'title difficulty')
  .sort({
    createdAt: 1 // Earlier submissions first (first to solve gets rank 1)
  })
  .limit(parseInt(limit))
  .skip((parseInt(page) - 1) * parseInt(limit));

  // Format solutions for frontend (ranked by submission time - first to solve gets rank 1)
  const formattedSolutions = solutions.map((solution, index) => ({
    id: solution._id,
    username: solution.user?.username || solution.user?.fullName || 'Anonymous',
    code: solution.code,
    language: solution.language,
    approach: solution.approach,
    timestamp: solution.createdAt,
    questionTitle: solution.question?.title,
    stats: {
      // Since new model doesn't have runtime/memory stats, we'll use placeholder values
      runtime: 'N/A',
      memory: 'N/A',
      runtimePercentile: null,
      memoryPercentile: null
    },
    rank: index + 1 // Rank based on submission order (first to solve = rank 1)
  }));

  res.status(StatusCodes.OK).json({
    success: true,
    count: formattedSolutions.length,
    solutions: formattedSolutions
  });
});

/**
 * Get all solutions by a specific user
 * @route GET /api/v1/solutions/user/:userId
 * @access Public
 */
const getSolutionsByUser = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { limit = 20, page = 1 } = req.query;

  const solutions = await Solution.find({ user: userId })
    .populate('user', 'fullName username')
    .populate('question', 'title difficulty')
    .sort({ createdAt: -1 })
    .limit(parseInt(limit))
    .skip((parseInt(page) - 1) * parseInt(limit));

  const formattedSolutions = solutions.map(solution => ({
    id: solution._id,
    questionTitle: solution.question?.title,
    difficulty: solution.question?.difficulty,
    language: solution.language,
    code: solution.code,
    approach: solution.approach,
    timestamp: solution.createdAt
  }));

  res.status(StatusCodes.OK).json({
    success: true,
    count: formattedSolutions.length,
    solutions: formattedSolutions
  });
});

/**
 * Get solution statistics
 * @route GET /api/v1/solutions/stats
 * @access Public
 */
const getSolutionStats = asyncHandler(async (req, res) => {
  const totalSolutions = await Solution.countDocuments();
  const uniqueUsers = await Solution.distinct('user').length;
  const uniqueQuestions = await Solution.distinct('question').length;

  // Get language distribution
  const languageStats = await Solution.aggregate([
    { $group: { _id: '$language', count: { $sum: 1 } } },
    { $sort: { count: -1 } }
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    stats: {
      totalSolutions,
      uniqueUsers,
      uniqueQuestions,
      languageDistribution: languageStats
    }
  });
});

export {
  saveSolution,
  getSolutionsByProblem,
  getSolutionsByUser,
  getSolutionStats
};
